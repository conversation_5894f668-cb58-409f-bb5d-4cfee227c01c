package httprouter

import (
	"context"
	"fmt"
	"github.com/foxcorp-product/commerce-purchase/constants"
	"net/http"
	"strings"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	"github.com/foxcorp-product/entitlement-sdk/jwtdecoder"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	"github.com/foxcorp-product/commerce-purchase/clients/subscription"
	"github.com/foxcorp-product/commerce-purchase/models"
	"github.com/foxcorp-product/commerce-purchase/utils"
)

type V1Subscription struct {
	Uid           string                        `json:"uid" api:"uid"`
	AltUserID     string                        `json:"altUserID,omitempty" api:"altUserID"`
	Subscriptions []models.V1SubscriptionObject `json:"subscriptions" api:"subscriptions"`
}

func (h V1PurchaseHandler) V1GetSubscriptions(w http.ResponseWriter, r *http.Request) {
	var (
		err  error
		span stats.Span
		ctx  = r.Context()
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions")
	defer func() { span.FinishWithError(err) }()
	r = r.WithContext(ctx)
	rh := request.GetFromContext(r.Context())
	rl := request.GetFromContext(ctx).GetLoggingEntry()

	jwtClaims := rh.GetJWTClaims()
	if jwtClaims == nil {
		rl.Errorf("error calling rh.GetJWTClaims(): %v", err)
		err = utils.ErrFieldRequired(fmt.Sprintf("header.%s OR header.%s for claims", jwtdecoder.AccessTokenHeader, jwtdecoder.AuthorizationHeader))
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	uid := jwtClaims.Uid
	if strings.TrimSpace(uid) == "" {
		sendError(ctx, w, "V1PurchaseHandler.V1GetSubscriptions", utils.NewErrBadRequest(errMissingUid))
		return
	}

	ctx, _, span = utils.ContextWithLogFieldsAndSpanTags(ctx, map[string]interface{}{
		"uid": uid,
	})
	res, err := h.d.clients.entitlements.PostS2sEntitlements(ctx, entitlement.PostS2sEntitlementsBody{
		Uid: uid,
	})
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	nationEntitlements := utils.FilterEntitlementsByAppId(res.Entitlements, constants.NationAppId)

	sendMigrationRequestEvent(ctx, h, nationEntitlements, jwtClaims)

	subs := &V1Subscription{
		Uid:           uid,
		Subscriptions: utils.EntitlementsToSubscriptions(res.Entitlements),
	}

	if h.hasActiveD2CFoxnationEntitlement(res.Entitlements) {
		rl.Infof("V1GetSubscriptions - uid: %v, has active d2c foxnation entitlement", uid)
		h.finalizeSubs(ctx, w, r, uid, subs)
		return
	}

	isUseEvergent, err := h.d.GetUseEvergent(ctx)
	if err != nil {
		sendError(ctx, w, "v1PurchaseHandler.V1GetSubscriptions", err)
		return
	}

	isActive := false
	if activeEnt := h.findActiveNationEntitlement(nationEntitlements); activeEnt != nil {
		virtualSub := h.createVirtualD2CFoxnationSub(*activeEnt)
		subs.Subscriptions = append(subs.Subscriptions, virtualSub)
		isActive = true
		rl.Infof("V1GetSubscriptions - uid: %v, has active foxnation entitlement in comm v2", uid)
	} else if isUseEvergent && h.shouldDoForwardToComm1(ctx, nationEntitlements) {
		if h.addVirtualSubFromComm1(ctx, uid, subs, rl) {
			rl.Infof("V1GetSubscriptions - uid: %v, has active foxnation in comm v1", uid)
		}
	}

	h.logSubscriptionStatus(ctx, uid, subs, isUseEvergent, isActive, res.Entitlements)
	h.finalizeSubs(ctx, w, r, uid, subs)
}

func (h V1PurchaseHandler) shouldDoForwardToComm1(ctx context.Context, entitlements entitlement.V1EntitlementEntitlements) bool {
	if len(entitlements) == 0 {
		return true
	}

	return h.isStoreIsOffForComm2(ctx, entitlements[0].PaymentMethod)
}

func (h V1PurchaseHandler) getSubscriptionFromCommV1(ctx context.Context, uid string) (int, *V1Subscription, error) {
	var (
		err  error
		span stats.Span
		rh   = request.GetFromContext(ctx)
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions.getSubscriptionFromCommV1")
	defer func() { span.FinishWithError(err) }()

	subs := &V1Subscription{Uid: uid}
	subs.Subscriptions = make([]models.V1SubscriptionObject, 0)
	HeaderParams := subscription.HeaderParams{
		XApigwApiId:  &h.d.cfg.VPCKeyCommEKSDelta,
		APIKey:       rh.GetAPIKey().APIKey,
		XAccessToken: fmt.Sprintf("Bearer %s", rh.GetJWTToken()),
	}

	hasSubscriptionReq := subscription.HasSubscriptionRequest{}
	resCommV1, code, err := h.d.clients.subscription.HasSubscription(ctx, hasSubscriptionReq, HeaderParams)
	if err != nil {
		return code, nil, err
	}

	if resCommV1 != nil {
		subs.Subscriptions = utils.SubscriptionsCommV1ToCommV2(resCommV1.ServiceDetails)
	}
	return code, subs, nil
}

func (h V1PurchaseHandler) setAltUIDToStripeUID(ctx context.Context, userID string, subs *V1Subscription) error {
	var (
		err  error
		span stats.Span
	)
	ctx, span = h.d.stat.StartMethodSpan(ctx, "v1PurchaseHandler.V1GetSubscriptions.setAltUIDToStripeUID")
	defer func() { span.FinishWithError(err) }()

	stripeCustomerId, err := h.getStripeCustomerId(ctx, userID)
	if err != nil && !isHttpErrorNotFound(err) {
		return newErrFailedStatusFailedDependency(err)
	}

	subs.AltUserID = stripeCustomerId
	return nil
}
