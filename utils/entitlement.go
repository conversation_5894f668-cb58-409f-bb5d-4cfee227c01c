package utils

import (
	"strings"
	"time"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	entitlementService "github.com/foxcorp-product/commerce-entitlement/entitlement"

	"github.com/foxcorp-product/commerce-purchase/models"
)

const commV1SubscriptionActiveStatus = "Active"
const commV1SubscriptionFinalBillStatus = "Final Bill"

func EntitlementsToSubscriptionsCommV1(entitlements []entitlement.V1EntitlementObject) ([]models.ServiceDetails, bool) {
	if len(entitlements) == 0 {
		return []models.ServiceDetails{}, false
	}

	pkgDetails := make([]models.PackageDetails, 0, len(entitlements))
	isSubscribed := false
	for _, ent := range entitlements {
		if ent.Status != string(entitlementService.EntitlementStatusChurnedInvoluntarily) && ent.Status != string(entitlementService.EntitlementStatusChurnedVoluntary) {
			isSubscribed = true
		}
		pkgDetails = append(pkgDetails, models.PackageDetails{
			OrderID:       ent.PurchaseId,
			ServiceID:     ent.AppServiceId,
			ServiceName:   ent.Description,
			PaymentMethod: ent.PaymentMethod,
			StartDate:     ent.StartDate.UnixMilli(),
			ValidityTill:  ent.ValidityTill.UnixMilli(),
			Tier:          int64(ent.Tier),
			Status:        ent.Status,
			ProductId: []models.ProductId{
				{
					ProductId: "foxnation",
				},
			},
		})
	}
	svcDetails := []models.ServiceDetails{
		{
			AppId:          "foxnation",
			PackageDetails: pkgDetails,
		},
	}
	return svcDetails, isSubscribed
}

func SubscriptionsCommV1ToCommV2(sd []models.ServiceDetails) []models.V1SubscriptionObject {
	subsCommV2 := make([]models.V1SubscriptionObject, 0, len(sd))
	for _, v := range sd {
		for _, pkgDetails := range v.PackageDetails {
			startDate := time.UnixMilli(pkgDetails.StartDate)
			validityTill := time.UnixMilli(pkgDetails.ValidityTill)
			serviceId := pkgDetails.ServiceID
			serviceName := pkgDetails.ServiceName
			status := strings.TrimSpace(pkgDetails.Status)
			active := status == commV1SubscriptionActiveStatus ||
				(status == commV1SubscriptionFinalBillStatus && pkgDetails.ValidityTill > time.Now().UnixMilli()) // or it's in pending cancel

			subscription := models.V1SubscriptionObject{
				AppServiceId: pkgDetails.ServiceID,
				Partner:      pkgDetails.PaymentMethod,
				StartDate:    &startDate,
				Status:       pkgDetails.Status,
				Active:       active,
				ValidityTill: &validityTill,
				PurchaseId:   pkgDetails.OrderID,
				AppId:        v.AppId,
				// ProductFamily: "", // TODO: Add ProductFamily after it is better defined
				Services: []models.V1SubscriptionServiceObject{
					{
						ServiceId:   &serviceId,
						ServiceName: &serviceName,
					},
				},
			}
			subsCommV2 = append(subsCommV2, subscription)
		}
	}
	return subsCommV2
}

func EntitlementsToSubscriptions(entitlements []entitlement.V1EntitlementObject) []models.V1SubscriptionObject {
	subs := make([]models.V1SubscriptionObject, 0, len(entitlements))
	for _, v := range entitlements {
		subscription := EntitlementToSubscription(v)
		subs = append(subs, subscription)
	}
	return subs
}

func EntitlementToSubscription(ent entitlement.V1EntitlementObject) models.V1SubscriptionObject {
	subscription := models.V1SubscriptionObject{
		AppServiceId:  ent.AppServiceId,
		Partner:       ent.PaymentMethod,
		StartDate:     &ent.StartDate,
		Status:        ent.Status,
		Active:        ent.Active,
		ValidityTill:  ent.ValidityTill,
		PurchaseId:    ent.PurchaseId,
		AppId:         ent.AppId,
		ProductFamily: ent.ProductFamily,
	}

	if ent.PendingServiceChange != nil {
		subscription.UpcomingAppServiceId = ent.PendingServiceChange.TargetAppServiceId
	}

	if len(ent.Services) > 0 {
		subscription.Services = make([]models.V1SubscriptionServiceObject, 0, len(ent.Services))
		for _, service := range ent.Services {
			subscription.Services = append(subscription.Services, models.V1SubscriptionServiceObject{
				ServiceId:   service.ServiceId,
				ServiceName: service.ServiceName,
			})
		}
	}

	if ent.Revocation != nil && ent.Revocation.ResumesAt != nil {
		subscription.ResumeAt = ent.Revocation.ResumesAt
	}

	if ent.Revocation != nil {
		subscription.ValidityTill = &ent.Revocation.Date
	}

	return subscription
}

func FilterEntitlementsByAppId(entitlements []entitlement.V1EntitlementObject, appId string) []entitlement.V1EntitlementObject {
	if len(entitlements) == 0 {
		return entitlements
	}

	return Filter(entitlements, func(entitlement entitlement.V1EntitlementObject) bool {
		return entitlement.AppId == appId
	})
}
